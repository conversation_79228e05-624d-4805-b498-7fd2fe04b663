('D:\\software\\code\\FlashToolApp\\build\\FastFlasher\\PYZ-00.pyz',
 [('PIL',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE-1'),
  ('PIL.AvifImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\AvifImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.BlpImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.BmpImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.BufrStubImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.CurImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.DcxImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.DdsImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.EpsImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.ExifTags',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE-1'),
  ('PIL.FitsImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.FliImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.FpxImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.FtexImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.GbrImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.GifImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.GimpGradientFile',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE-1'),
  ('PIL.GimpPaletteFile',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE-1'),
  ('PIL.GribStubImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.IcnsImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.IcoImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.ImImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.Image',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE-1'),
  ('PIL.ImageChops',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE-1'),
  ('PIL.ImageCms',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE-1'),
  ('PIL.ImageColor',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE-1'),
  ('PIL.ImageDraw',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImageDraw.py',
   'PYMODULE-1'),
  ('PIL.ImageDraw2',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImageDraw2.py',
   'PYMODULE-1'),
  ('PIL.ImageFile',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE-1'),
  ('PIL.ImageFilter',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE-1'),
  ('PIL.ImageFont',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImageFont.py',
   'PYMODULE-1'),
  ('PIL.ImageMath',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE-1'),
  ('PIL.ImageMode',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE-1'),
  ('PIL.ImageOps',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE-1'),
  ('PIL.ImagePalette',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE-1'),
  ('PIL.ImagePath',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImagePath.py',
   'PYMODULE-1'),
  ('PIL.ImageQt',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE-1'),
  ('PIL.ImageSequence',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE-1'),
  ('PIL.ImageShow',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE-1'),
  ('PIL.ImageTk',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE-1'),
  ('PIL.ImageWin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE-1'),
  ('PIL.ImtImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.IptcImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.JpegImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.JpegPresets',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE-1'),
  ('PIL.McIdasImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.MicImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.MpegImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.MpoImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.MspImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PaletteFile',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE-1'),
  ('PIL.PalmImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PcdImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PcxImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PdfImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PdfParser',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE-1'),
  ('PIL.PixarImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PngImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PpmImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.PsdImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.QoiImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.SgiImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.SpiderImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.SunImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.TgaImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.TiffImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.TiffTags',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE-1'),
  ('PIL.WebPImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.WmfImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.XVThumbImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.XbmImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE-1'),
  ('PIL.XpmImagePlugin',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE-1'),
  ('PIL._binary',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE-1'),
  ('PIL._deprecate',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE-1'),
  ('PIL._typing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE-1'),
  ('PIL._util',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE-1'),
  ('PIL._version',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE-1'),
  ('PIL.features',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE-1'),
  ('__future__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\__future__.py',
   'PYMODULE-1'),
  ('_aix_support',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_aix_support.py',
   'PYMODULE-1'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_compat_pickle.py',
   'PYMODULE-1'),
  ('_compression',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_compression.py',
   'PYMODULE-1'),
  ('_distutils_hack',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE-1'),
  ('_distutils_hack.override',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE-1'),
  ('_py_abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_py_abc.py',
   'PYMODULE-1'),
  ('_pydatetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_pydatetime.py',
   'PYMODULE-1'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_pydecimal.py',
   'PYMODULE-1'),
  ('_sitebuiltins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_sitebuiltins.py',
   'PYMODULE-1'),
  ('_strptime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_strptime.py',
   'PYMODULE-1'),
  ('_threading_local',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\_threading_local.py',
   'PYMODULE-1'),
  ('argparse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\argparse.py',
   'PYMODULE-1'),
  ('ast',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ast.py',
   'PYMODULE-1'),
  ('asyncio',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\__init__.py',
   'PYMODULE-1'),
  ('asyncio.base_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_events.py',
   'PYMODULE-1'),
  ('asyncio.base_futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_futures.py',
   'PYMODULE-1'),
  ('asyncio.base_subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE-1'),
  ('asyncio.base_tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE-1'),
  ('asyncio.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\constants.py',
   'PYMODULE-1'),
  ('asyncio.coroutines',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\coroutines.py',
   'PYMODULE-1'),
  ('asyncio.events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\events.py',
   'PYMODULE-1'),
  ('asyncio.exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\exceptions.py',
   'PYMODULE-1'),
  ('asyncio.format_helpers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE-1'),
  ('asyncio.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\futures.py',
   'PYMODULE-1'),
  ('asyncio.locks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\locks.py',
   'PYMODULE-1'),
  ('asyncio.log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\log.py',
   'PYMODULE-1'),
  ('asyncio.mixins',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\mixins.py',
   'PYMODULE-1'),
  ('asyncio.proactor_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE-1'),
  ('asyncio.protocols',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\protocols.py',
   'PYMODULE-1'),
  ('asyncio.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\queues.py',
   'PYMODULE-1'),
  ('asyncio.runners',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\runners.py',
   'PYMODULE-1'),
  ('asyncio.selector_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\selector_events.py',
   'PYMODULE-1'),
  ('asyncio.sslproto',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\sslproto.py',
   'PYMODULE-1'),
  ('asyncio.staggered',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\staggered.py',
   'PYMODULE-1'),
  ('asyncio.streams',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\streams.py',
   'PYMODULE-1'),
  ('asyncio.subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\subprocess.py',
   'PYMODULE-1'),
  ('asyncio.taskgroups',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE-1'),
  ('asyncio.tasks',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\tasks.py',
   'PYMODULE-1'),
  ('asyncio.threads',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\threads.py',
   'PYMODULE-1'),
  ('asyncio.timeouts',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\timeouts.py',
   'PYMODULE-1'),
  ('asyncio.transports',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\transports.py',
   'PYMODULE-1'),
  ('asyncio.trsock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\trsock.py',
   'PYMODULE-1'),
  ('asyncio.unix_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\unix_events.py',
   'PYMODULE-1'),
  ('asyncio.windows_events',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\windows_events.py',
   'PYMODULE-1'),
  ('asyncio.windows_utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE-1'),
  ('backports',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE-1'),
  ('base64',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\base64.py',
   'PYMODULE-1'),
  ('bdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\bdb.py',
   'PYMODULE-1'),
  ('bisect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\bisect.py',
   'PYMODULE-1'),
  ('bz2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\bz2.py',
   'PYMODULE-1'),
  ('calendar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\calendar.py',
   'PYMODULE-1'),
  ('certifi',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\certifi\\__init__.py',
   'PYMODULE-1'),
  ('certifi.core',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\certifi\\core.py',
   'PYMODULE-1'),
  ('charset_normalizer',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE-1'),
  ('charset_normalizer.api',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE-1'),
  ('charset_normalizer.cd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE-1'),
  ('charset_normalizer.constant',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE-1'),
  ('charset_normalizer.legacy',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE-1'),
  ('charset_normalizer.models',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE-1'),
  ('charset_normalizer.utils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE-1'),
  ('charset_normalizer.version',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE-1'),
  ('cmd',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\cmd.py',
   'PYMODULE-1'),
  ('code',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\code.py',
   'PYMODULE-1'),
  ('codeop',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\codeop.py',
   'PYMODULE-1'),
  ('colorsys',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\colorsys.py',
   'PYMODULE-1'),
  ('concurrent',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\concurrent\\__init__.py',
   'PYMODULE-1'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE-1'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE-1'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE-1'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE-1'),
  ('configparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\configparser.py',
   'PYMODULE-1'),
  ('contextlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\contextlib.py',
   'PYMODULE-1'),
  ('contextvars',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\contextvars.py',
   'PYMODULE-1'),
  ('copy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\copy.py',
   'PYMODULE-1'),
  ('cryptography',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.__about__',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\__about__.py',
   'PYMODULE-1'),
  ('cryptography.exceptions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE-1'),
  ('cryptography.hazmat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat._oid',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.backends',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.backends.openssl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.backends.openssl.backend',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.bindings',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.bindings.openssl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.decrepit',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.decrepit.ciphers',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.decrepit.ciphers.algorithms',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\decrepit\\ciphers\\algorithms.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives._asymmetric',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives._serialization',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.ciphers',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.constant_time',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.hashes',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.serialization',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.serialization.base',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE-1'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE-1'),
  ('cryptography.utils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\utils.py',
   'PYMODULE-1'),
  ('cryptography.x509',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE-1'),
  ('cryptography.x509.base',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE-1'),
  ('cryptography.x509.certificate_transparency',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE-1'),
  ('cryptography.x509.extensions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE-1'),
  ('cryptography.x509.general_name',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE-1'),
  ('cryptography.x509.name',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE-1'),
  ('cryptography.x509.oid',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE-1'),
  ('cryptography.x509.verification',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\cryptography\\x509\\verification.py',
   'PYMODULE-1'),
  ('csv',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\csv.py',
   'PYMODULE-1'),
  ('ctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\__init__.py',
   'PYMODULE-1'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\_endian.py',
   'PYMODULE-1'),
  ('ctypes.wintypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ctypes\\wintypes.py',
   'PYMODULE-1'),
  ('dataclasses',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\dataclasses.py',
   'PYMODULE-1'),
  ('datetime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\datetime.py',
   'PYMODULE-1'),
  ('dateutil',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\dateutil\\__init__.py',
   'PYMODULE-1'),
  ('dateutil._common',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\dateutil\\_common.py',
   'PYMODULE-1'),
  ('dateutil._version',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\dateutil\\_version.py',
   'PYMODULE-1'),
  ('dateutil.easter',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\dateutil\\easter.py',
   'PYMODULE-1'),
  ('dateutil.parser',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\dateutil\\parser\\__init__.py',
   'PYMODULE-1'),
  ('dateutil.parser._parser',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\dateutil\\parser\\_parser.py',
   'PYMODULE-1'),
  ('dateutil.parser.isoparser',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\dateutil\\parser\\isoparser.py',
   'PYMODULE-1'),
  ('dateutil.relativedelta',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\dateutil\\relativedelta.py',
   'PYMODULE-1'),
  ('dateutil.rrule',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\dateutil\\rrule.py',
   'PYMODULE-1'),
  ('dateutil.tz',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\dateutil\\tz\\__init__.py',
   'PYMODULE-1'),
  ('dateutil.tz._common',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\dateutil\\tz\\_common.py',
   'PYMODULE-1'),
  ('dateutil.tz._factories',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\dateutil\\tz\\_factories.py',
   'PYMODULE-1'),
  ('dateutil.tz.tz',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\dateutil\\tz\\tz.py',
   'PYMODULE-1'),
  ('dateutil.tz.win',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\dateutil\\tz\\win.py',
   'PYMODULE-1'),
  ('dateutil.zoneinfo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\dateutil\\zoneinfo\\__init__.py',
   'PYMODULE-1'),
  ('decimal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\decimal.py',
   'PYMODULE-1'),
  ('difflib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\difflib.py',
   'PYMODULE-1'),
  ('dis',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\dis.py',
   'PYMODULE-1'),
  ('doctest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\doctest.py',
   'PYMODULE-1'),
  ('email',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\__init__.py',
   'PYMODULE-1'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_encoded_words.py',
   'PYMODULE-1'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_header_value_parser.py',
   'PYMODULE-1'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_parseaddr.py',
   'PYMODULE-1'),
  ('email._policybase',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\_policybase.py',
   'PYMODULE-1'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\base64mime.py',
   'PYMODULE-1'),
  ('email.charset',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\charset.py',
   'PYMODULE-1'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\contentmanager.py',
   'PYMODULE-1'),
  ('email.encoders',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\encoders.py',
   'PYMODULE-1'),
  ('email.errors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\errors.py',
   'PYMODULE-1'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\feedparser.py',
   'PYMODULE-1'),
  ('email.generator',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\generator.py',
   'PYMODULE-1'),
  ('email.header',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\header.py',
   'PYMODULE-1'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\headerregistry.py',
   'PYMODULE-1'),
  ('email.iterators',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\iterators.py',
   'PYMODULE-1'),
  ('email.message',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\message.py',
   'PYMODULE-1'),
  ('email.parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\parser.py',
   'PYMODULE-1'),
  ('email.policy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\policy.py',
   'PYMODULE-1'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\quoprimime.py',
   'PYMODULE-1'),
  ('email.utils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\email\\utils.py',
   'PYMODULE-1'),
  ('esptool',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\__init__.py',
   'PYMODULE-1'),
  ('esptool.__main__',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\__main__.py',
   'PYMODULE-1'),
  ('esptool.bin_image',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\bin_image.py',
   'PYMODULE-1'),
  ('esptool.cmds',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\cmds.py',
   'PYMODULE-1'),
  ('esptool.config',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\config.py',
   'PYMODULE-1'),
  ('esptool.loader',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\loader.py',
   'PYMODULE-1'),
  ('esptool.reset',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\reset.py',
   'PYMODULE-1'),
  ('esptool.targets',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\targets\\__init__.py',
   'PYMODULE-1'),
  ('esptool.targets.esp32',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\targets\\esp32.py',
   'PYMODULE-1'),
  ('esptool.targets.esp32c2',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\targets\\esp32c2.py',
   'PYMODULE-1'),
  ('esptool.targets.esp32c3',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\targets\\esp32c3.py',
   'PYMODULE-1'),
  ('esptool.targets.esp32c5',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\targets\\esp32c5.py',
   'PYMODULE-1'),
  ('esptool.targets.esp32c5beta3',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\targets\\esp32c5beta3.py',
   'PYMODULE-1'),
  ('esptool.targets.esp32c6',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\targets\\esp32c6.py',
   'PYMODULE-1'),
  ('esptool.targets.esp32c61',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\targets\\esp32c61.py',
   'PYMODULE-1'),
  ('esptool.targets.esp32c6beta',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\targets\\esp32c6beta.py',
   'PYMODULE-1'),
  ('esptool.targets.esp32h2',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\targets\\esp32h2.py',
   'PYMODULE-1'),
  ('esptool.targets.esp32h2beta1',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\targets\\esp32h2beta1.py',
   'PYMODULE-1'),
  ('esptool.targets.esp32h2beta2',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\targets\\esp32h2beta2.py',
   'PYMODULE-1'),
  ('esptool.targets.esp32p4',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\targets\\esp32p4.py',
   'PYMODULE-1'),
  ('esptool.targets.esp32s2',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\targets\\esp32s2.py',
   'PYMODULE-1'),
  ('esptool.targets.esp32s3',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\targets\\esp32s3.py',
   'PYMODULE-1'),
  ('esptool.targets.esp32s3beta2',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\targets\\esp32s3beta2.py',
   'PYMODULE-1'),
  ('esptool.targets.esp8266',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\targets\\esp8266.py',
   'PYMODULE-1'),
  ('esptool.uf2_writer',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\uf2_writer.py',
   'PYMODULE-1'),
  ('esptool.util',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\esptool\\util.py',
   'PYMODULE-1'),
  ('et_xmlfile',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE-1'),
  ('et_xmlfile.incremental_tree',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\et_xmlfile\\incremental_tree.py',
   'PYMODULE-1'),
  ('et_xmlfile.xmlfile',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE-1'),
  ('fileinput',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\fileinput.py',
   'PYMODULE-1'),
  ('fnmatch',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\fnmatch.py',
   'PYMODULE-1'),
  ('fractions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\fractions.py',
   'PYMODULE-1'),
  ('ftplib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ftplib.py',
   'PYMODULE-1'),
  ('getopt',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\getopt.py',
   'PYMODULE-1'),
  ('getpass',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\getpass.py',
   'PYMODULE-1'),
  ('gettext',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\gettext.py',
   'PYMODULE-1'),
  ('glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\glob.py',
   'PYMODULE-1'),
  ('gzip',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\gzip.py',
   'PYMODULE-1'),
  ('hashlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\hashlib.py',
   'PYMODULE-1'),
  ('hmac',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\hmac.py',
   'PYMODULE-1'),
  ('html',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\html\\__init__.py',
   'PYMODULE-1'),
  ('html.entities',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\html\\entities.py',
   'PYMODULE-1'),
  ('http',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\http\\__init__.py',
   'PYMODULE-1'),
  ('http.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\http\\client.py',
   'PYMODULE-1'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\http\\cookiejar.py',
   'PYMODULE-1'),
  ('http.cookies',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\http\\cookies.py',
   'PYMODULE-1'),
  ('http.server',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\http\\server.py',
   'PYMODULE-1'),
  ('idna',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\idna\\__init__.py',
   'PYMODULE-1'),
  ('idna.core',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\idna\\core.py',
   'PYMODULE-1'),
  ('idna.idnadata',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\idna\\idnadata.py',
   'PYMODULE-1'),
  ('idna.intranges',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\idna\\intranges.py',
   'PYMODULE-1'),
  ('idna.package_data',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\idna\\package_data.py',
   'PYMODULE-1'),
  ('idna.uts46data',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\idna\\uts46data.py',
   'PYMODULE-1'),
  ('importlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\__init__.py',
   'PYMODULE-1'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\_abc.py',
   'PYMODULE-1'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE-1'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE-1'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\abc.py',
   'PYMODULE-1'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\machinery.py',
   'PYMODULE-1'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE-1'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE-1'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE-1'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE-1'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE-1'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE-1'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE-1'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\readers.py',
   'PYMODULE-1'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE-1'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE-1'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE-1'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE-1'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE-1'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE-1'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE-1'),
  ('importlib.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\importlib\\util.py',
   'PYMODULE-1'),
  ('inspect',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\inspect.py',
   'PYMODULE-1'),
  ('intelhex',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\intelhex\\__init__.py',
   'PYMODULE-1'),
  ('intelhex.compat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\intelhex\\compat.py',
   'PYMODULE-1'),
  ('intelhex.getsizeof',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\intelhex\\getsizeof.py',
   'PYMODULE-1'),
  ('ipaddress',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ipaddress.py',
   'PYMODULE-1'),
  ('jaraco', '-', 'PYMODULE-1'),
  ('json',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\__init__.py',
   'PYMODULE-1'),
  ('json.decoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\decoder.py',
   'PYMODULE-1'),
  ('json.encoder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\encoder.py',
   'PYMODULE-1'),
  ('json.scanner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\json\\scanner.py',
   'PYMODULE-1'),
  ('logging',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\logging\\__init__.py',
   'PYMODULE-1'),
  ('lzma',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\lzma.py',
   'PYMODULE-1'),
  ('mimetypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\mimetypes.py',
   'PYMODULE-1'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE-1'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\connection.py',
   'PYMODULE-1'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\context.py',
   'PYMODULE-1'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE-1'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE-1'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE-1'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\heap.py',
   'PYMODULE-1'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\managers.py',
   'PYMODULE-1'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\pool.py',
   'PYMODULE-1'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE-1'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE-1'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE-1'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE-1'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\process.py',
   'PYMODULE-1'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\queues.py',
   'PYMODULE-1'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE-1'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE-1'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE-1'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE-1'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE-1'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE-1'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE-1'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\multiprocessing\\util.py',
   'PYMODULE-1'),
  ('netrc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\netrc.py',
   'PYMODULE-1'),
  ('nturl2path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\nturl2path.py',
   'PYMODULE-1'),
  ('numbers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\numbers.py',
   'PYMODULE-1'),
  ('numpy',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE-1'),
  ('numpy.__config__',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE-1'),
  ('numpy._array_api_info',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_array_api_info.py',
   'PYMODULE-1'),
  ('numpy._core',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\__init__.py',
   'PYMODULE-1'),
  ('numpy._core._add_newdocs',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs.py',
   'PYMODULE-1'),
  ('numpy._core._add_newdocs_scalars',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\_add_newdocs_scalars.py',
   'PYMODULE-1'),
  ('numpy._core._asarray',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\_asarray.py',
   'PYMODULE-1'),
  ('numpy._core._dtype',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype.py',
   'PYMODULE-1'),
  ('numpy._core._dtype_ctypes',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\_dtype_ctypes.py',
   'PYMODULE-1'),
  ('numpy._core._internal',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\_internal.py',
   'PYMODULE-1'),
  ('numpy._core._machar',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\_machar.py',
   'PYMODULE-1'),
  ('numpy._core._methods',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\_methods.py',
   'PYMODULE-1'),
  ('numpy._core._string_helpers',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\_string_helpers.py',
   'PYMODULE-1'),
  ('numpy._core._type_aliases',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\_type_aliases.py',
   'PYMODULE-1'),
  ('numpy._core._ufunc_config',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\_ufunc_config.py',
   'PYMODULE-1'),
  ('numpy._core.arrayprint',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\arrayprint.py',
   'PYMODULE-1'),
  ('numpy._core.defchararray',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\defchararray.py',
   'PYMODULE-1'),
  ('numpy._core.einsumfunc',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\einsumfunc.py',
   'PYMODULE-1'),
  ('numpy._core.fromnumeric',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\fromnumeric.py',
   'PYMODULE-1'),
  ('numpy._core.function_base',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\function_base.py',
   'PYMODULE-1'),
  ('numpy._core.getlimits',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\getlimits.py',
   'PYMODULE-1'),
  ('numpy._core.memmap',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\memmap.py',
   'PYMODULE-1'),
  ('numpy._core.multiarray',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\multiarray.py',
   'PYMODULE-1'),
  ('numpy._core.numeric',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\numeric.py',
   'PYMODULE-1'),
  ('numpy._core.numerictypes',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\numerictypes.py',
   'PYMODULE-1'),
  ('numpy._core.overrides',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\overrides.py',
   'PYMODULE-1'),
  ('numpy._core.printoptions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\printoptions.py',
   'PYMODULE-1'),
  ('numpy._core.records',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\records.py',
   'PYMODULE-1'),
  ('numpy._core.shape_base',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\shape_base.py',
   'PYMODULE-1'),
  ('numpy._core.strings',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\strings.py',
   'PYMODULE-1'),
  ('numpy._core.umath',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_core\\umath.py',
   'PYMODULE-1'),
  ('numpy._distributor_init',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE-1'),
  ('numpy._expired_attrs_2_0',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_expired_attrs_2_0.py',
   'PYMODULE-1'),
  ('numpy._globals',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE-1'),
  ('numpy._pytesttester',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE-1'),
  ('numpy._typing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE-1'),
  ('numpy._typing._add_docstring',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE-1'),
  ('numpy._typing._array_like',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE-1'),
  ('numpy._typing._char_codes',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE-1'),
  ('numpy._typing._dtype_like',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE-1'),
  ('numpy._typing._nbit',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE-1'),
  ('numpy._typing._nbit_base',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nbit_base.py',
   'PYMODULE-1'),
  ('numpy._typing._nested_sequence',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE-1'),
  ('numpy._typing._scalars',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE-1'),
  ('numpy._typing._shape',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE-1'),
  ('numpy._typing._ufunc',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_typing\\_ufunc.py',
   'PYMODULE-1'),
  ('numpy._utils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_utils\\__init__.py',
   'PYMODULE-1'),
  ('numpy._utils._convertions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_utils\\_convertions.py',
   'PYMODULE-1'),
  ('numpy._utils._inspect',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\_utils\\_inspect.py',
   'PYMODULE-1'),
  ('numpy.char',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\char\\__init__.py',
   'PYMODULE-1'),
  ('numpy.core',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE-1'),
  ('numpy.core._utils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\core\\_utils.py',
   'PYMODULE-1'),
  ('numpy.ctypeslib',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\ctypeslib\\__init__.py',
   'PYMODULE-1'),
  ('numpy.ctypeslib._ctypeslib',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\ctypeslib\\_ctypeslib.py',
   'PYMODULE-1'),
  ('numpy.dtypes',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\dtypes.py',
   'PYMODULE-1'),
  ('numpy.exceptions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\exceptions.py',
   'PYMODULE-1'),
  ('numpy.f2py',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\__init__.py',
   'PYMODULE-1'),
  ('numpy.f2py.__version__',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\__version__.py',
   'PYMODULE-1'),
  ('numpy.f2py._backends',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\__init__.py',
   'PYMODULE-1'),
  ('numpy.f2py._backends._backend',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_backend.py',
   'PYMODULE-1'),
  ('numpy.f2py._backends._distutils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_distutils.py',
   'PYMODULE-1'),
  ('numpy.f2py._backends._meson',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\_backends\\_meson.py',
   'PYMODULE-1'),
  ('numpy.f2py._isocbind',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\_isocbind.py',
   'PYMODULE-1'),
  ('numpy.f2py.auxfuncs',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\auxfuncs.py',
   'PYMODULE-1'),
  ('numpy.f2py.capi_maps',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\capi_maps.py',
   'PYMODULE-1'),
  ('numpy.f2py.cb_rules',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\cb_rules.py',
   'PYMODULE-1'),
  ('numpy.f2py.cfuncs',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\cfuncs.py',
   'PYMODULE-1'),
  ('numpy.f2py.common_rules',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\common_rules.py',
   'PYMODULE-1'),
  ('numpy.f2py.crackfortran',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\crackfortran.py',
   'PYMODULE-1'),
  ('numpy.f2py.diagnose',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\diagnose.py',
   'PYMODULE-1'),
  ('numpy.f2py.f2py2e',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\f2py2e.py',
   'PYMODULE-1'),
  ('numpy.f2py.f90mod_rules',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\f90mod_rules.py',
   'PYMODULE-1'),
  ('numpy.f2py.func2subr',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\func2subr.py',
   'PYMODULE-1'),
  ('numpy.f2py.rules',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\rules.py',
   'PYMODULE-1'),
  ('numpy.f2py.symbolic',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\symbolic.py',
   'PYMODULE-1'),
  ('numpy.f2py.use_rules',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\f2py\\use_rules.py',
   'PYMODULE-1'),
  ('numpy.fft',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE-1'),
  ('numpy.fft._helper',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\fft\\_helper.py',
   'PYMODULE-1'),
  ('numpy.fft._pocketfft',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE-1'),
  ('numpy.fft.helper',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE-1'),
  ('numpy.lib',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE-1'),
  ('numpy.lib._array_utils_impl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_array_utils_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._arraypad_impl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraypad_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._arraysetops_impl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_arraysetops_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._arrayterator_impl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_arrayterator_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._datasource',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE-1'),
  ('numpy.lib._format_impl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_format_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._function_base_impl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_function_base_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._histograms_impl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_histograms_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._index_tricks_impl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_index_tricks_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._iotools',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE-1'),
  ('numpy.lib._nanfunctions_impl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_nanfunctions_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._npyio_impl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_npyio_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._polynomial_impl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_polynomial_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._scimath_impl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_scimath_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._shape_base_impl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_shape_base_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._stride_tricks_impl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_stride_tricks_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._twodim_base_impl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_twodim_base_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._type_check_impl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_type_check_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._ufunclike_impl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_ufunclike_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._utils_impl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_utils_impl.py',
   'PYMODULE-1'),
  ('numpy.lib._version',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE-1'),
  ('numpy.lib.array_utils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\array_utils.py',
   'PYMODULE-1'),
  ('numpy.lib.format',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE-1'),
  ('numpy.lib.introspect',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\introspect.py',
   'PYMODULE-1'),
  ('numpy.lib.mixins',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE-1'),
  ('numpy.lib.npyio',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE-1'),
  ('numpy.lib.recfunctions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\recfunctions.py',
   'PYMODULE-1'),
  ('numpy.lib.scimath',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE-1'),
  ('numpy.lib.stride_tricks',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE-1'),
  ('numpy.linalg',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE-1'),
  ('numpy.linalg._linalg',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\linalg\\_linalg.py',
   'PYMODULE-1'),
  ('numpy.linalg.linalg',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE-1'),
  ('numpy.ma',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE-1'),
  ('numpy.ma.core',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE-1'),
  ('numpy.ma.extras',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE-1'),
  ('numpy.ma.mrecords',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE-1'),
  ('numpy.matlib',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\matlib.py',
   'PYMODULE-1'),
  ('numpy.matrixlib',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE-1'),
  ('numpy.matrixlib.defmatrix',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE-1'),
  ('numpy.polynomial',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE-1'),
  ('numpy.polynomial._polybase',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE-1'),
  ('numpy.polynomial.chebyshev',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE-1'),
  ('numpy.polynomial.hermite',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE-1'),
  ('numpy.polynomial.hermite_e',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE-1'),
  ('numpy.polynomial.laguerre',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE-1'),
  ('numpy.polynomial.legendre',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE-1'),
  ('numpy.polynomial.polynomial',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE-1'),
  ('numpy.polynomial.polyutils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE-1'),
  ('numpy.random',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE-1'),
  ('numpy.random._pickle',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE-1'),
  ('numpy.rec',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\rec\\__init__.py',
   'PYMODULE-1'),
  ('numpy.strings',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\strings\\__init__.py',
   'PYMODULE-1'),
  ('numpy.testing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE-1'),
  ('numpy.testing._private',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE-1'),
  ('numpy.testing._private.extbuild',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE-1'),
  ('numpy.testing._private.utils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE-1'),
  ('numpy.testing.overrides',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\testing\\overrides.py',
   'PYMODULE-1'),
  ('numpy.typing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE-1'),
  ('numpy.version',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE-1'),
  ('opcode',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\opcode.py',
   'PYMODULE-1'),
  ('openpyxl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl._constants',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE-1'),
  ('openpyxl.cell',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.cell._writer',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE-1'),
  ('openpyxl.cell.cell',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE-1'),
  ('openpyxl.cell.read_only',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE-1'),
  ('openpyxl.cell.rich_text',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE-1'),
  ('openpyxl.cell.text',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE-1'),
  ('openpyxl.chart',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.chart._3d',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE-1'),
  ('openpyxl.chart._chart',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.area_chart',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.axis',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE-1'),
  ('openpyxl.chart.bar_chart',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.bubble_chart',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.chartspace',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE-1'),
  ('openpyxl.chart.data_source',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE-1'),
  ('openpyxl.chart.descriptors',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE-1'),
  ('openpyxl.chart.error_bar',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE-1'),
  ('openpyxl.chart.label',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE-1'),
  ('openpyxl.chart.layout',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE-1'),
  ('openpyxl.chart.legend',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE-1'),
  ('openpyxl.chart.line_chart',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.marker',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE-1'),
  ('openpyxl.chart.picture',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE-1'),
  ('openpyxl.chart.pie_chart',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.pivot',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE-1'),
  ('openpyxl.chart.plotarea',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE-1'),
  ('openpyxl.chart.print_settings',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE-1'),
  ('openpyxl.chart.radar_chart',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.reader',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE-1'),
  ('openpyxl.chart.reference',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE-1'),
  ('openpyxl.chart.scatter_chart',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.series',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE-1'),
  ('openpyxl.chart.series_factory',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE-1'),
  ('openpyxl.chart.shapes',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE-1'),
  ('openpyxl.chart.stock_chart',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.surface_chart',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE-1'),
  ('openpyxl.chart.text',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE-1'),
  ('openpyxl.chart.title',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE-1'),
  ('openpyxl.chart.trendline',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE-1'),
  ('openpyxl.chart.updown_bars',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE-1'),
  ('openpyxl.chartsheet',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE-1'),
  ('openpyxl.chartsheet.custom',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE-1'),
  ('openpyxl.chartsheet.properties',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE-1'),
  ('openpyxl.chartsheet.protection',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE-1'),
  ('openpyxl.chartsheet.publish',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE-1'),
  ('openpyxl.chartsheet.relation',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE-1'),
  ('openpyxl.chartsheet.views',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE-1'),
  ('openpyxl.comments',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.comments.author',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE-1'),
  ('openpyxl.comments.comment_sheet',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE-1'),
  ('openpyxl.comments.comments',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE-1'),
  ('openpyxl.comments.shape_writer',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE-1'),
  ('openpyxl.compat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.compat.numbers',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE-1'),
  ('openpyxl.compat.strings',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE-1'),
  ('openpyxl.descriptors',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.descriptors.base',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE-1'),
  ('openpyxl.descriptors.container',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\container.py',
   'PYMODULE-1'),
  ('openpyxl.descriptors.excel',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE-1'),
  ('openpyxl.descriptors.namespace',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE-1'),
  ('openpyxl.descriptors.nested',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE-1'),
  ('openpyxl.descriptors.sequence',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE-1'),
  ('openpyxl.descriptors.serialisable',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE-1'),
  ('openpyxl.drawing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.colors',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.connector',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.drawing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.effect',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.fill',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.geometry',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.graphic',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.image',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.line',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.picture',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.properties',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.relation',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.text',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE-1'),
  ('openpyxl.drawing.xdr',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE-1'),
  ('openpyxl.formatting',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.formatting.formatting',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE-1'),
  ('openpyxl.formatting.rule',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE-1'),
  ('openpyxl.formula',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.formula.tokenizer',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE-1'),
  ('openpyxl.formula.translate',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE-1'),
  ('openpyxl.packaging',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.packaging.core',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE-1'),
  ('openpyxl.packaging.custom',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE-1'),
  ('openpyxl.packaging.extended',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE-1'),
  ('openpyxl.packaging.manifest',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE-1'),
  ('openpyxl.packaging.relationship',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE-1'),
  ('openpyxl.packaging.workbook',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE-1'),
  ('openpyxl.pivot',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.pivot.cache',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE-1'),
  ('openpyxl.pivot.fields',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE-1'),
  ('openpyxl.pivot.record',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE-1'),
  ('openpyxl.pivot.table',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE-1'),
  ('openpyxl.reader',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.reader.drawings',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE-1'),
  ('openpyxl.reader.excel',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE-1'),
  ('openpyxl.reader.strings',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE-1'),
  ('openpyxl.reader.workbook',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE-1'),
  ('openpyxl.styles',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.styles.alignment',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE-1'),
  ('openpyxl.styles.borders',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE-1'),
  ('openpyxl.styles.builtins',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE-1'),
  ('openpyxl.styles.cell_style',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE-1'),
  ('openpyxl.styles.colors',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE-1'),
  ('openpyxl.styles.differential',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE-1'),
  ('openpyxl.styles.fills',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE-1'),
  ('openpyxl.styles.fonts',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE-1'),
  ('openpyxl.styles.named_styles',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE-1'),
  ('openpyxl.styles.numbers',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE-1'),
  ('openpyxl.styles.protection',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE-1'),
  ('openpyxl.styles.proxy',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE-1'),
  ('openpyxl.styles.styleable',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE-1'),
  ('openpyxl.styles.stylesheet',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE-1'),
  ('openpyxl.styles.table',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE-1'),
  ('openpyxl.utils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE-1'),
  ('openpyxl.utils.cell',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE-1'),
  ('openpyxl.utils.datetime',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE-1'),
  ('openpyxl.utils.escape',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE-1'),
  ('openpyxl.utils.exceptions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE-1'),
  ('openpyxl.utils.formulas',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE-1'),
  ('openpyxl.utils.indexed_list',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE-1'),
  ('openpyxl.utils.protection',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE-1'),
  ('openpyxl.utils.units',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE-1'),
  ('openpyxl.workbook',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.workbook._writer',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.child',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.defined_name',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.external_link',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.external_link.external',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.external_reference',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.function_group',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.properties',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.protection',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.smart_tags',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.views',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.web',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE-1'),
  ('openpyxl.workbook.workbook',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet._read_only',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet._reader',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet._write_only',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet._writer',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.cell_range',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.copier',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.dimensions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.drawing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.filters',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.formula',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.header_footer',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.merge',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.page',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.print_settings',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.properties',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.protection',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.related',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.scenario',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.table',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.views',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE-1'),
  ('openpyxl.worksheet.worksheet',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE-1'),
  ('openpyxl.writer',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.writer.excel',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE-1'),
  ('openpyxl.writer.theme',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE-1'),
  ('openpyxl.xml',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE-1'),
  ('openpyxl.xml.constants',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE-1'),
  ('openpyxl.xml.functions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE-1'),
  ('packaging',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE-1'),
  ('packaging._elffile',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE-1'),
  ('packaging._manylinux',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE-1'),
  ('packaging._musllinux',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE-1'),
  ('packaging._parser',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE-1'),
  ('packaging._structures',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE-1'),
  ('packaging._tokenizer',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE-1'),
  ('packaging.licenses',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\packaging\\licenses\\__init__.py',
   'PYMODULE-1'),
  ('packaging.licenses._spdx',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\packaging\\licenses\\_spdx.py',
   'PYMODULE-1'),
  ('packaging.markers',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE-1'),
  ('packaging.metadata',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE-1'),
  ('packaging.requirements',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE-1'),
  ('packaging.specifiers',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE-1'),
  ('packaging.tags',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE-1'),
  ('packaging.utils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE-1'),
  ('packaging.version',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE-1'),
  ('pandas',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\__init__.py',
   'PYMODULE-1'),
  ('pandas._config',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_config\\__init__.py',
   'PYMODULE-1'),
  ('pandas._config.config',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_config\\config.py',
   'PYMODULE-1'),
  ('pandas._config.dates',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_config\\dates.py',
   'PYMODULE-1'),
  ('pandas._config.display',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_config\\display.py',
   'PYMODULE-1'),
  ('pandas._config.localization',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_config\\localization.py',
   'PYMODULE-1'),
  ('pandas._libs',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\__init__.py',
   'PYMODULE-1'),
  ('pandas._libs.tslibs',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\tslibs\\__init__.py',
   'PYMODULE-1'),
  ('pandas._libs.window',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_libs\\window\\__init__.py',
   'PYMODULE-1'),
  ('pandas._testing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_testing\\__init__.py',
   'PYMODULE-1'),
  ('pandas._testing._io',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_testing\\_io.py',
   'PYMODULE-1'),
  ('pandas._testing._warnings',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_testing\\_warnings.py',
   'PYMODULE-1'),
  ('pandas._testing.asserters',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_testing\\asserters.py',
   'PYMODULE-1'),
  ('pandas._testing.compat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_testing\\compat.py',
   'PYMODULE-1'),
  ('pandas._testing.contexts',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_testing\\contexts.py',
   'PYMODULE-1'),
  ('pandas._typing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_typing.py',
   'PYMODULE-1'),
  ('pandas._version',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_version.py',
   'PYMODULE-1'),
  ('pandas._version_meson',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\_version_meson.py',
   'PYMODULE-1'),
  ('pandas.api',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\api\\__init__.py',
   'PYMODULE-1'),
  ('pandas.api.extensions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\api\\extensions\\__init__.py',
   'PYMODULE-1'),
  ('pandas.api.indexers',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\api\\indexers\\__init__.py',
   'PYMODULE-1'),
  ('pandas.api.interchange',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\api\\interchange\\__init__.py',
   'PYMODULE-1'),
  ('pandas.api.types',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\api\\types\\__init__.py',
   'PYMODULE-1'),
  ('pandas.api.typing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\api\\typing\\__init__.py',
   'PYMODULE-1'),
  ('pandas.arrays',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\arrays\\__init__.py',
   'PYMODULE-1'),
  ('pandas.compat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\compat\\__init__.py',
   'PYMODULE-1'),
  ('pandas.compat._constants',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\compat\\_constants.py',
   'PYMODULE-1'),
  ('pandas.compat._optional',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\compat\\_optional.py',
   'PYMODULE-1'),
  ('pandas.compat.compressors',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\compat\\compressors.py',
   'PYMODULE-1'),
  ('pandas.compat.numpy',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\compat\\numpy\\__init__.py',
   'PYMODULE-1'),
  ('pandas.compat.numpy.function',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\compat\\numpy\\function.py',
   'PYMODULE-1'),
  ('pandas.compat.pickle_compat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\compat\\pickle_compat.py',
   'PYMODULE-1'),
  ('pandas.compat.pyarrow',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\compat\\pyarrow.py',
   'PYMODULE-1'),
  ('pandas.core',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core._numba',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core._numba.executor',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\executor.py',
   'PYMODULE-1'),
  ('pandas.core._numba.extensions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\extensions.py',
   'PYMODULE-1'),
  ('pandas.core._numba.kernels',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core._numba.kernels.mean_',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\mean_.py',
   'PYMODULE-1'),
  ('pandas.core._numba.kernels.min_max_',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\min_max_.py',
   'PYMODULE-1'),
  ('pandas.core._numba.kernels.shared',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\shared.py',
   'PYMODULE-1'),
  ('pandas.core._numba.kernels.sum_',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\sum_.py',
   'PYMODULE-1'),
  ('pandas.core._numba.kernels.var_',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\_numba\\kernels\\var_.py',
   'PYMODULE-1'),
  ('pandas.core.accessor',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\accessor.py',
   'PYMODULE-1'),
  ('pandas.core.algorithms',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\algorithms.py',
   'PYMODULE-1'),
  ('pandas.core.api',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\api.py',
   'PYMODULE-1'),
  ('pandas.core.apply',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\apply.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.datetimelike_accumulations',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\datetimelike_accumulations.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.masked_accumulations',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_accumulations.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.masked_reductions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\masked_reductions.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.putmask',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\putmask.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.quantile',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\quantile.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.replace',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\replace.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.take',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\take.py',
   'PYMODULE-1'),
  ('pandas.core.array_algos.transforms',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\array_algos\\transforms.py',
   'PYMODULE-1'),
  ('pandas.core.arraylike',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arraylike.py',
   'PYMODULE-1'),
  ('pandas.core.arrays',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.arrays._arrow_string_mixins',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_arrow_string_mixins.py',
   'PYMODULE-1'),
  ('pandas.core.arrays._mixins',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_mixins.py',
   'PYMODULE-1'),
  ('pandas.core.arrays._ranges',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_ranges.py',
   'PYMODULE-1'),
  ('pandas.core.arrays._utils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\_utils.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.arrow',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.arrow._arrow_utils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\_arrow_utils.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.arrow.accessors',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\accessors.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.arrow.array',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\array.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.arrow.extension_types',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\arrow\\extension_types.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.base',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\base.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.boolean',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\boolean.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.categorical',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\categorical.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.datetimelike',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.datetimes',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\datetimes.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.floating',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\floating.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.integer',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\integer.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.interval',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\interval.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.masked',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\masked.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.numeric',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\numeric.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.numpy_',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\numpy_.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.period',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\period.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.sparse',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.sparse.accessor',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\accessor.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.sparse.array',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\array.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.sparse.scipy_sparse',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\sparse\\scipy_sparse.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.string_',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.string_arrow',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\string_arrow.py',
   'PYMODULE-1'),
  ('pandas.core.arrays.timedeltas',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\arrays\\timedeltas.py',
   'PYMODULE-1'),
  ('pandas.core.base',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\base.py',
   'PYMODULE-1'),
  ('pandas.core.common',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\common.py',
   'PYMODULE-1'),
  ('pandas.core.computation',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.computation.align',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\align.py',
   'PYMODULE-1'),
  ('pandas.core.computation.api',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\api.py',
   'PYMODULE-1'),
  ('pandas.core.computation.check',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\check.py',
   'PYMODULE-1'),
  ('pandas.core.computation.common',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\common.py',
   'PYMODULE-1'),
  ('pandas.core.computation.engines',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\engines.py',
   'PYMODULE-1'),
  ('pandas.core.computation.eval',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\eval.py',
   'PYMODULE-1'),
  ('pandas.core.computation.expr',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\expr.py',
   'PYMODULE-1'),
  ('pandas.core.computation.expressions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\expressions.py',
   'PYMODULE-1'),
  ('pandas.core.computation.ops',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\ops.py',
   'PYMODULE-1'),
  ('pandas.core.computation.parsing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\parsing.py',
   'PYMODULE-1'),
  ('pandas.core.computation.pytables',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\pytables.py',
   'PYMODULE-1'),
  ('pandas.core.computation.scope',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\computation\\scope.py',
   'PYMODULE-1'),
  ('pandas.core.config_init',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\config_init.py',
   'PYMODULE-1'),
  ('pandas.core.construction',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\construction.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.api',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\api.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.astype',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\astype.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.base',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\base.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.cast',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\cast.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.common',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\common.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.concat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\concat.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.dtypes',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\dtypes.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.generic',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\generic.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.inference',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\inference.py',
   'PYMODULE-1'),
  ('pandas.core.dtypes.missing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\dtypes\\missing.py',
   'PYMODULE-1'),
  ('pandas.core.flags',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\flags.py',
   'PYMODULE-1'),
  ('pandas.core.frame',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\frame.py',
   'PYMODULE-1'),
  ('pandas.core.generic',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\generic.py',
   'PYMODULE-1'),
  ('pandas.core.groupby',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.base',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\base.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.categorical',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\categorical.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.generic',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\generic.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.groupby',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\groupby.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.grouper',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\grouper.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.indexing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\indexing.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.numba_',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\numba_.py',
   'PYMODULE-1'),
  ('pandas.core.groupby.ops',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\groupby\\ops.py',
   'PYMODULE-1'),
  ('pandas.core.indexers',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.indexers.objects',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\objects.py',
   'PYMODULE-1'),
  ('pandas.core.indexers.utils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\indexers\\utils.py',
   'PYMODULE-1'),
  ('pandas.core.indexes',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.accessors',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\accessors.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.api',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\api.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.base',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\base.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.category',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\category.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.datetimelike',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimelike.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.datetimes',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\datetimes.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.extension',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\extension.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.frozen',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\frozen.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.interval',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\interval.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.multi',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\multi.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.period',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\period.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.range',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\range.py',
   'PYMODULE-1'),
  ('pandas.core.indexes.timedeltas',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\indexes\\timedeltas.py',
   'PYMODULE-1'),
  ('pandas.core.indexing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\indexing.py',
   'PYMODULE-1'),
  ('pandas.core.interchange',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.interchange.buffer',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\buffer.py',
   'PYMODULE-1'),
  ('pandas.core.interchange.column',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\column.py',
   'PYMODULE-1'),
  ('pandas.core.interchange.dataframe',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe.py',
   'PYMODULE-1'),
  ('pandas.core.interchange.dataframe_protocol',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\dataframe_protocol.py',
   'PYMODULE-1'),
  ('pandas.core.interchange.from_dataframe',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\from_dataframe.py',
   'PYMODULE-1'),
  ('pandas.core.interchange.utils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\interchange\\utils.py',
   'PYMODULE-1'),
  ('pandas.core.internals',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.internals.api',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\api.py',
   'PYMODULE-1'),
  ('pandas.core.internals.array_manager',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\array_manager.py',
   'PYMODULE-1'),
  ('pandas.core.internals.base',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\base.py',
   'PYMODULE-1'),
  ('pandas.core.internals.blocks',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\blocks.py',
   'PYMODULE-1'),
  ('pandas.core.internals.concat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\concat.py',
   'PYMODULE-1'),
  ('pandas.core.internals.construction',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\construction.py',
   'PYMODULE-1'),
  ('pandas.core.internals.managers',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\managers.py',
   'PYMODULE-1'),
  ('pandas.core.internals.ops',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\internals\\ops.py',
   'PYMODULE-1'),
  ('pandas.core.methods',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.methods.describe',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\describe.py',
   'PYMODULE-1'),
  ('pandas.core.methods.selectn',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\selectn.py',
   'PYMODULE-1'),
  ('pandas.core.methods.to_dict',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\methods\\to_dict.py',
   'PYMODULE-1'),
  ('pandas.core.missing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\missing.py',
   'PYMODULE-1'),
  ('pandas.core.nanops',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\nanops.py',
   'PYMODULE-1'),
  ('pandas.core.ops',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.ops.array_ops',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py',
   'PYMODULE-1'),
  ('pandas.core.ops.common',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\common.py',
   'PYMODULE-1'),
  ('pandas.core.ops.dispatch',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\dispatch.py',
   'PYMODULE-1'),
  ('pandas.core.ops.docstrings',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\docstrings.py',
   'PYMODULE-1'),
  ('pandas.core.ops.invalid',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\invalid.py',
   'PYMODULE-1'),
  ('pandas.core.ops.mask_ops',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\mask_ops.py',
   'PYMODULE-1'),
  ('pandas.core.ops.missing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\ops\\missing.py',
   'PYMODULE-1'),
  ('pandas.core.resample',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\resample.py',
   'PYMODULE-1'),
  ('pandas.core.reshape',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.api',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\api.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.concat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\concat.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.encoding',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\encoding.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.melt',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\melt.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.merge',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\merge.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.pivot',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\pivot.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.reshape',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\reshape.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.tile',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\tile.py',
   'PYMODULE-1'),
  ('pandas.core.reshape.util',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\reshape\\util.py',
   'PYMODULE-1'),
  ('pandas.core.roperator',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\roperator.py',
   'PYMODULE-1'),
  ('pandas.core.sample',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\sample.py',
   'PYMODULE-1'),
  ('pandas.core.series',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\series.py',
   'PYMODULE-1'),
  ('pandas.core.shared_docs',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\shared_docs.py',
   'PYMODULE-1'),
  ('pandas.core.sorting',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\sorting.py',
   'PYMODULE-1'),
  ('pandas.core.strings',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.strings.accessor',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\accessor.py',
   'PYMODULE-1'),
  ('pandas.core.strings.base',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\base.py',
   'PYMODULE-1'),
  ('pandas.core.strings.object_array',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\strings\\object_array.py',
   'PYMODULE-1'),
  ('pandas.core.tools',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.tools.datetimes',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\datetimes.py',
   'PYMODULE-1'),
  ('pandas.core.tools.numeric',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\numeric.py',
   'PYMODULE-1'),
  ('pandas.core.tools.timedeltas',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\timedeltas.py',
   'PYMODULE-1'),
  ('pandas.core.tools.times',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\tools\\times.py',
   'PYMODULE-1'),
  ('pandas.core.util',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\util\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.util.hashing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\util\\hashing.py',
   'PYMODULE-1'),
  ('pandas.core.util.numba_',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\util\\numba_.py',
   'PYMODULE-1'),
  ('pandas.core.window',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\window\\__init__.py',
   'PYMODULE-1'),
  ('pandas.core.window.common',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\window\\common.py',
   'PYMODULE-1'),
  ('pandas.core.window.doc',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\window\\doc.py',
   'PYMODULE-1'),
  ('pandas.core.window.ewm',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\window\\ewm.py',
   'PYMODULE-1'),
  ('pandas.core.window.expanding',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\window\\expanding.py',
   'PYMODULE-1'),
  ('pandas.core.window.numba_',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\window\\numba_.py',
   'PYMODULE-1'),
  ('pandas.core.window.online',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\window\\online.py',
   'PYMODULE-1'),
  ('pandas.core.window.rolling',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\core\\window\\rolling.py',
   'PYMODULE-1'),
  ('pandas.errors',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\errors\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io._util',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\_util.py',
   'PYMODULE-1'),
  ('pandas.io.api',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\api.py',
   'PYMODULE-1'),
  ('pandas.io.clipboard',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\clipboard\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io.clipboards',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\clipboards.py',
   'PYMODULE-1'),
  ('pandas.io.common',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\common.py',
   'PYMODULE-1'),
  ('pandas.io.excel',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io.excel._base',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_base.py',
   'PYMODULE-1'),
  ('pandas.io.excel._calamine',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_calamine.py',
   'PYMODULE-1'),
  ('pandas.io.excel._odfreader',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_odfreader.py',
   'PYMODULE-1'),
  ('pandas.io.excel._odswriter',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_odswriter.py',
   'PYMODULE-1'),
  ('pandas.io.excel._openpyxl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_openpyxl.py',
   'PYMODULE-1'),
  ('pandas.io.excel._pyxlsb',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_pyxlsb.py',
   'PYMODULE-1'),
  ('pandas.io.excel._util',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_util.py',
   'PYMODULE-1'),
  ('pandas.io.excel._xlrd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlrd.py',
   'PYMODULE-1'),
  ('pandas.io.excel._xlsxwriter',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\excel\\_xlsxwriter.py',
   'PYMODULE-1'),
  ('pandas.io.feather_format',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\feather_format.py',
   'PYMODULE-1'),
  ('pandas.io.formats',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io.formats._color_data',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\_color_data.py',
   'PYMODULE-1'),
  ('pandas.io.formats.console',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\console.py',
   'PYMODULE-1'),
  ('pandas.io.formats.css',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\css.py',
   'PYMODULE-1'),
  ('pandas.io.formats.csvs',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\csvs.py',
   'PYMODULE-1'),
  ('pandas.io.formats.excel',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\excel.py',
   'PYMODULE-1'),
  ('pandas.io.formats.format',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\format.py',
   'PYMODULE-1'),
  ('pandas.io.formats.html',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\html.py',
   'PYMODULE-1'),
  ('pandas.io.formats.info',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\info.py',
   'PYMODULE-1'),
  ('pandas.io.formats.printing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\printing.py',
   'PYMODULE-1'),
  ('pandas.io.formats.string',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\string.py',
   'PYMODULE-1'),
  ('pandas.io.formats.style',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\style.py',
   'PYMODULE-1'),
  ('pandas.io.formats.style_render',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\style_render.py',
   'PYMODULE-1'),
  ('pandas.io.formats.xml',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\formats\\xml.py',
   'PYMODULE-1'),
  ('pandas.io.gbq',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\gbq.py',
   'PYMODULE-1'),
  ('pandas.io.html',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\html.py',
   'PYMODULE-1'),
  ('pandas.io.json',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\json\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io.json._json',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_json.py',
   'PYMODULE-1'),
  ('pandas.io.json._normalize',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_normalize.py',
   'PYMODULE-1'),
  ('pandas.io.json._table_schema',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\json\\_table_schema.py',
   'PYMODULE-1'),
  ('pandas.io.orc',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\orc.py',
   'PYMODULE-1'),
  ('pandas.io.parquet',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\parquet.py',
   'PYMODULE-1'),
  ('pandas.io.parsers',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io.parsers.arrow_parser_wrapper',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\arrow_parser_wrapper.py',
   'PYMODULE-1'),
  ('pandas.io.parsers.base_parser',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\base_parser.py',
   'PYMODULE-1'),
  ('pandas.io.parsers.c_parser_wrapper',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\c_parser_wrapper.py',
   'PYMODULE-1'),
  ('pandas.io.parsers.python_parser',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\python_parser.py',
   'PYMODULE-1'),
  ('pandas.io.parsers.readers',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py',
   'PYMODULE-1'),
  ('pandas.io.pickle',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\pickle.py',
   'PYMODULE-1'),
  ('pandas.io.pytables',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\pytables.py',
   'PYMODULE-1'),
  ('pandas.io.sas',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\__init__.py',
   'PYMODULE-1'),
  ('pandas.io.sas.sas7bdat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas7bdat.py',
   'PYMODULE-1'),
  ('pandas.io.sas.sas_constants',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_constants.py',
   'PYMODULE-1'),
  ('pandas.io.sas.sas_xport',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sas_xport.py',
   'PYMODULE-1'),
  ('pandas.io.sas.sasreader',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\sas\\sasreader.py',
   'PYMODULE-1'),
  ('pandas.io.spss',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\spss.py',
   'PYMODULE-1'),
  ('pandas.io.sql',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\sql.py',
   'PYMODULE-1'),
  ('pandas.io.stata',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\stata.py',
   'PYMODULE-1'),
  ('pandas.io.xml',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\io\\xml.py',
   'PYMODULE-1'),
  ('pandas.plotting',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\plotting\\__init__.py',
   'PYMODULE-1'),
  ('pandas.plotting._core',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\plotting\\_core.py',
   'PYMODULE-1'),
  ('pandas.plotting._misc',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\plotting\\_misc.py',
   'PYMODULE-1'),
  ('pandas.testing',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\testing.py',
   'PYMODULE-1'),
  ('pandas.tseries',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\tseries\\__init__.py',
   'PYMODULE-1'),
  ('pandas.tseries.api',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\tseries\\api.py',
   'PYMODULE-1'),
  ('pandas.tseries.frequencies',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\tseries\\frequencies.py',
   'PYMODULE-1'),
  ('pandas.tseries.holiday',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\tseries\\holiday.py',
   'PYMODULE-1'),
  ('pandas.tseries.offsets',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\tseries\\offsets.py',
   'PYMODULE-1'),
  ('pandas.util',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\util\\__init__.py',
   'PYMODULE-1'),
  ('pandas.util._decorators',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\util\\_decorators.py',
   'PYMODULE-1'),
  ('pandas.util._exceptions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\util\\_exceptions.py',
   'PYMODULE-1'),
  ('pandas.util._print_versions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\util\\_print_versions.py',
   'PYMODULE-1'),
  ('pandas.util._tester',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\util\\_tester.py',
   'PYMODULE-1'),
  ('pandas.util._validators',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\util\\_validators.py',
   'PYMODULE-1'),
  ('pandas.util.version',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pandas\\util\\version\\__init__.py',
   'PYMODULE-1'),
  ('pathlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pathlib.py',
   'PYMODULE-1'),
  ('pdb',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pdb.py',
   'PYMODULE-1'),
  ('pickle',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pickle.py',
   'PYMODULE-1'),
  ('pickletools',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pickletools.py',
   'PYMODULE-1'),
  ('pkg_resources',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE-1'),
  ('pkgutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pkgutil.py',
   'PYMODULE-1'),
  ('platform',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\platform.py',
   'PYMODULE-1'),
  ('plistlib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\plistlib.py',
   'PYMODULE-1'),
  ('pprint',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pprint.py',
   'PYMODULE-1'),
  ('py_compile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\py_compile.py',
   'PYMODULE-1'),
  ('pydoc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pydoc.py',
   'PYMODULE-1'),
  ('pydoc_data',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE-1'),
  ('pydoc_data.topics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\pydoc_data\\topics.py',
   'PYMODULE-1'),
  ('pytz',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\__init__.py',
   'PYMODULE-1'),
  ('pytz.exceptions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\exceptions.py',
   'PYMODULE-1'),
  ('pytz.lazy',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\lazy.py',
   'PYMODULE-1'),
  ('pytz.tzfile',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\tzfile.py',
   'PYMODULE-1'),
  ('pytz.tzinfo',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\pytz\\tzinfo.py',
   'PYMODULE-1'),
  ('qrcode',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\qrcode\\__init__.py',
   'PYMODULE-1'),
  ('qrcode.LUT',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\qrcode\\LUT.py',
   'PYMODULE-1'),
  ('qrcode.base',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\qrcode\\base.py',
   'PYMODULE-1'),
  ('qrcode.compat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\qrcode\\compat\\__init__.py',
   'PYMODULE-1'),
  ('qrcode.compat.png',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\qrcode\\compat\\png.py',
   'PYMODULE-1'),
  ('qrcode.constants',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\qrcode\\constants.py',
   'PYMODULE-1'),
  ('qrcode.exceptions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\qrcode\\exceptions.py',
   'PYMODULE-1'),
  ('qrcode.image',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\qrcode\\image\\__init__.py',
   'PYMODULE-1'),
  ('qrcode.image.base',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\qrcode\\image\\base.py',
   'PYMODULE-1'),
  ('qrcode.image.pil',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\qrcode\\image\\pil.py',
   'PYMODULE-1'),
  ('qrcode.image.pure',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\qrcode\\image\\pure.py',
   'PYMODULE-1'),
  ('qrcode.image.styledpil',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\qrcode\\image\\styledpil.py',
   'PYMODULE-1'),
  ('qrcode.image.styles',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\qrcode\\image\\styles\\__init__.py',
   'PYMODULE-1'),
  ('qrcode.image.styles.colormasks',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\qrcode\\image\\styles\\colormasks.py',
   'PYMODULE-1'),
  ('qrcode.image.styles.moduledrawers',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\qrcode\\image\\styles\\moduledrawers\\__init__.py',
   'PYMODULE-1'),
  ('qrcode.image.styles.moduledrawers.base',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\qrcode\\image\\styles\\moduledrawers\\base.py',
   'PYMODULE-1'),
  ('qrcode.image.styles.moduledrawers.pil',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\qrcode\\image\\styles\\moduledrawers\\pil.py',
   'PYMODULE-1'),
  ('qrcode.main',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\qrcode\\main.py',
   'PYMODULE-1'),
  ('qrcode.util',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\qrcode\\util.py',
   'PYMODULE-1'),
  ('queue',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\queue.py',
   'PYMODULE-1'),
  ('quopri',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\quopri.py',
   'PYMODULE-1'),
  ('random',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\random.py',
   'PYMODULE-1'),
  ('requests',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\requests\\__init__.py',
   'PYMODULE-1'),
  ('requests.__version__',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\requests\\__version__.py',
   'PYMODULE-1'),
  ('requests._internal_utils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE-1'),
  ('requests.adapters',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\requests\\adapters.py',
   'PYMODULE-1'),
  ('requests.api',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\requests\\api.py',
   'PYMODULE-1'),
  ('requests.auth',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\requests\\auth.py',
   'PYMODULE-1'),
  ('requests.certs',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\requests\\certs.py',
   'PYMODULE-1'),
  ('requests.compat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\requests\\compat.py',
   'PYMODULE-1'),
  ('requests.cookies',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\requests\\cookies.py',
   'PYMODULE-1'),
  ('requests.exceptions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\requests\\exceptions.py',
   'PYMODULE-1'),
  ('requests.hooks',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\requests\\hooks.py',
   'PYMODULE-1'),
  ('requests.models',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\requests\\models.py',
   'PYMODULE-1'),
  ('requests.packages',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\requests\\packages.py',
   'PYMODULE-1'),
  ('requests.sessions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\requests\\sessions.py',
   'PYMODULE-1'),
  ('requests.status_codes',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\requests\\status_codes.py',
   'PYMODULE-1'),
  ('requests.structures',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\requests\\structures.py',
   'PYMODULE-1'),
  ('requests.utils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\requests\\utils.py',
   'PYMODULE-1'),
  ('rlcompleter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\rlcompleter.py',
   'PYMODULE-1'),
  ('runpy',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\runpy.py',
   'PYMODULE-1'),
  ('secrets',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\secrets.py',
   'PYMODULE-1'),
  ('selectors',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\selectors.py',
   'PYMODULE-1'),
  ('serial',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\serial\\__init__.py',
   'PYMODULE-1'),
  ('serial.serialcli',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\serial\\serialcli.py',
   'PYMODULE-1'),
  ('serial.serialjava',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\serial\\serialjava.py',
   'PYMODULE-1'),
  ('serial.serialposix',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\serial\\serialposix.py',
   'PYMODULE-1'),
  ('serial.serialutil',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\serial\\serialutil.py',
   'PYMODULE-1'),
  ('serial.serialwin32',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\serial\\serialwin32.py',
   'PYMODULE-1'),
  ('serial.tools',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\serial\\tools\\__init__.py',
   'PYMODULE-1'),
  ('serial.tools.list_ports',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports.py',
   'PYMODULE-1'),
  ('serial.tools.list_ports_common',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_common.py',
   'PYMODULE-1'),
  ('serial.tools.list_ports_linux',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_linux.py',
   'PYMODULE-1'),
  ('serial.tools.list_ports_osx',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_osx.py',
   'PYMODULE-1'),
  ('serial.tools.list_ports_posix',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_posix.py',
   'PYMODULE-1'),
  ('serial.tools.list_ports_windows',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\serial\\tools\\list_ports_windows.py',
   'PYMODULE-1'),
  ('serial.win32',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\serial\\win32.py',
   'PYMODULE-1'),
  ('setuptools',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._core_metadata',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE-1'),
  ('setuptools._distutils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._distutils._log',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE-1'),
  ('setuptools._distutils._modified',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE-1'),
  ('setuptools._distutils._msvccompiler',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE-1'),
  ('setuptools._distutils.archive_util',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE-1'),
  ('setuptools._distutils.ccompiler',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE-1'),
  ('setuptools._distutils.cmd',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.bdist',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.build',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.build_ext',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE-1'),
  ('setuptools._distutils.command.sdist',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE-1'),
  ('setuptools._distutils.compat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._distutils.compat.numpy',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\numpy.py',
   'PYMODULE-1'),
  ('setuptools._distutils.compat.py39',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py39.py',
   'PYMODULE-1'),
  ('setuptools._distutils.compilers', '-', 'PYMODULE-1'),
  ('setuptools._distutils.compilers.C', '-', 'PYMODULE-1'),
  ('setuptools._distutils.compilers.C.base',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\base.py',
   'PYMODULE-1'),
  ('setuptools._distutils.compilers.C.errors',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\errors.py',
   'PYMODULE-1'),
  ('setuptools._distutils.compilers.C.msvc',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\msvc.py',
   'PYMODULE-1'),
  ('setuptools._distutils.core',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE-1'),
  ('setuptools._distutils.debug',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE-1'),
  ('setuptools._distutils.dir_util',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE-1'),
  ('setuptools._distutils.dist',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE-1'),
  ('setuptools._distutils.errors',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE-1'),
  ('setuptools._distutils.extension',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE-1'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE-1'),
  ('setuptools._distutils.file_util',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE-1'),
  ('setuptools._distutils.filelist',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE-1'),
  ('setuptools._distutils.log',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE-1'),
  ('setuptools._distutils.spawn',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE-1'),
  ('setuptools._distutils.sysconfig',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE-1'),
  ('setuptools._distutils.text_file',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE-1'),
  ('setuptools._distutils.util',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE-1'),
  ('setuptools._distutils.version',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE-1'),
  ('setuptools._distutils.versionpredicate',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE-1'),
  ('setuptools._entry_points',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE-1'),
  ('setuptools._imp',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE-1'),
  ('setuptools._importlib',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE-1'),
  ('setuptools._itertools',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE-1'),
  ('setuptools._normalization',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE-1'),
  ('setuptools._path',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE-1'),
  ('setuptools._reqs',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE-1'),
  ('setuptools._shutil',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_shutil.py',
   'PYMODULE-1'),
  ('setuptools._static',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_static.py',
   'PYMODULE-1'),
  ('setuptools._vendor', '-', 'PYMODULE-1'),
  ('setuptools._vendor.backports',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.backports.tarfile.compat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.backports.tarfile.compat.py38',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile\\compat\\py38.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata.compat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata.compat.py311',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py311.py',
   'PYMODULE-1'),
  ('setuptools._vendor.importlib_metadata.compat.py39',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\compat\\py39.py',
   'PYMODULE-1'),
  ('setuptools._vendor.jaraco', '-', 'PYMODULE-1'),
  ('setuptools._vendor.jaraco.context',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE-1'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.jaraco.text',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.more_itertools',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE-1'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging._parser',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging._structures',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging.markers',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging.tags',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging.utils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE-1'),
  ('setuptools._vendor.packaging.version',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE-1'),
  ('setuptools._vendor.platformdirs',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.platformdirs.android',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\android.py',
   'PYMODULE-1'),
  ('setuptools._vendor.platformdirs.api',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\api.py',
   'PYMODULE-1'),
  ('setuptools._vendor.platformdirs.macos',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\macos.py',
   'PYMODULE-1'),
  ('setuptools._vendor.platformdirs.unix',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\unix.py',
   'PYMODULE-1'),
  ('setuptools._vendor.platformdirs.version',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\version.py',
   'PYMODULE-1'),
  ('setuptools._vendor.platformdirs.windows',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\platformdirs\\windows.py',
   'PYMODULE-1'),
  ('setuptools._vendor.tomli',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.tomli._parser',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE-1'),
  ('setuptools._vendor.tomli._re',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE-1'),
  ('setuptools._vendor.tomli._types',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.cli',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.cli.convert',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\convert.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.cli.pack',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\pack.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.cli.tags',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\tags.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.cli.unpack',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\cli\\unpack.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.macosx_libfile',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\macosx_libfile.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.metadata',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\metadata.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.util',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\util.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.vendored',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.vendored.packaging',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.vendored.packaging._elffile',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_elffile.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.vendored.packaging._manylinux',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_manylinux.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.vendored.packaging._musllinux',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_musllinux.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.vendored.packaging._parser',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_parser.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.vendored.packaging._structures',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_structures.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.vendored.packaging._tokenizer',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\_tokenizer.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.vendored.packaging.markers',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\markers.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.vendored.packaging.requirements',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\requirements.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.vendored.packaging.specifiers',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\specifiers.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.vendored.packaging.tags',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\tags.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.vendored.packaging.utils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\utils.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.vendored.packaging.version',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\vendored\\packaging\\version.py',
   'PYMODULE-1'),
  ('setuptools._vendor.wheel.wheelfile',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\wheel\\wheelfile.py',
   'PYMODULE-1'),
  ('setuptools._vendor.zipp',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.zipp.compat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\__init__.py',
   'PYMODULE-1'),
  ('setuptools._vendor.zipp.compat.py310',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\compat\\py310.py',
   'PYMODULE-1'),
  ('setuptools._vendor.zipp.glob',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\_vendor\\zipp\\glob.py',
   'PYMODULE-1'),
  ('setuptools.archive_util',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE-1'),
  ('setuptools.command',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE-1'),
  ('setuptools.command._requirestxt',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE-1'),
  ('setuptools.command.bdist_egg',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE-1'),
  ('setuptools.command.bdist_wheel',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\command\\bdist_wheel.py',
   'PYMODULE-1'),
  ('setuptools.command.build',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE-1'),
  ('setuptools.command.egg_info',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE-1'),
  ('setuptools.command.sdist',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE-1'),
  ('setuptools.command.setopt',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE-1'),
  ('setuptools.compat',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE-1'),
  ('setuptools.compat.py310',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE-1'),
  ('setuptools.compat.py311',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\compat\\py311.py',
   'PYMODULE-1'),
  ('setuptools.compat.py39',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\compat\\py39.py',
   'PYMODULE-1'),
  ('setuptools.config',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE-1'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE-1'),
  ('setuptools.config._validate_pyproject',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE-1'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE-1'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE-1'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE-1'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE-1'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE-1'),
  ('setuptools.config.expand',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE-1'),
  ('setuptools.config.pyprojecttoml',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE-1'),
  ('setuptools.config.setupcfg',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE-1'),
  ('setuptools.depends',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE-1'),
  ('setuptools.discovery',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE-1'),
  ('setuptools.dist',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE-1'),
  ('setuptools.errors',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE-1'),
  ('setuptools.extension',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE-1'),
  ('setuptools.glob',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE-1'),
  ('setuptools.installer',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE-1'),
  ('setuptools.logging',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE-1'),
  ('setuptools.monkey',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE-1'),
  ('setuptools.msvc',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE-1'),
  ('setuptools.unicode_utils',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE-1'),
  ('setuptools.version',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE-1'),
  ('setuptools.warnings',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE-1'),
  ('setuptools.wheel',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE-1'),
  ('setuptools.windows_support',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE-1'),
  ('shlex',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\shlex.py',
   'PYMODULE-1'),
  ('shutil',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\shutil.py',
   'PYMODULE-1'),
  ('signal',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\signal.py',
   'PYMODULE-1'),
  ('site',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site.py',
   'PYMODULE-1'),
  ('six',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\six.py',
   'PYMODULE-1'),
  ('socket',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socket.py',
   'PYMODULE-1'),
  ('socketserver',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\socketserver.py',
   'PYMODULE-1'),
  ('sqlite3',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\sqlite3\\__init__.py',
   'PYMODULE-1'),
  ('sqlite3.__main__',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\sqlite3\\__main__.py',
   'PYMODULE-1'),
  ('sqlite3.dbapi2',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\sqlite3\\dbapi2.py',
   'PYMODULE-1'),
  ('sqlite3.dump',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\sqlite3\\dump.py',
   'PYMODULE-1'),
  ('ssl',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\ssl.py',
   'PYMODULE-1'),
  ('statistics',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\statistics.py',
   'PYMODULE-1'),
  ('string',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\string.py',
   'PYMODULE-1'),
  ('stringprep',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\stringprep.py',
   'PYMODULE-1'),
  ('subprocess',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\subprocess.py',
   'PYMODULE-1'),
  ('sysconfig',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\sysconfig.py',
   'PYMODULE-1'),
  ('tarfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tarfile.py',
   'PYMODULE-1'),
  ('tempfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tempfile.py',
   'PYMODULE-1'),
  ('textwrap',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\textwrap.py',
   'PYMODULE-1'),
  ('threading',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\threading.py',
   'PYMODULE-1'),
  ('tkinter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tkinter\\__init__.py',
   'PYMODULE-1'),
  ('tkinter.commondialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tkinter\\commondialog.py',
   'PYMODULE-1'),
  ('tkinter.constants',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tkinter\\constants.py',
   'PYMODULE-1'),
  ('tkinter.dialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tkinter\\dialog.py',
   'PYMODULE-1'),
  ('tkinter.filedialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tkinter\\filedialog.py',
   'PYMODULE-1'),
  ('tkinter.messagebox',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tkinter\\messagebox.py',
   'PYMODULE-1'),
  ('tkinter.simpledialog',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tkinter\\simpledialog.py',
   'PYMODULE-1'),
  ('tkinter.ttk',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tkinter\\ttk.py',
   'PYMODULE-1'),
  ('token',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\token.py',
   'PYMODULE-1'),
  ('tokenize',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tokenize.py',
   'PYMODULE-1'),
  ('tomllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tomllib\\__init__.py',
   'PYMODULE-1'),
  ('tomllib._parser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tomllib\\_parser.py',
   'PYMODULE-1'),
  ('tomllib._re',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tomllib\\_re.py',
   'PYMODULE-1'),
  ('tomllib._types',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tomllib\\_types.py',
   'PYMODULE-1'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tracemalloc.py',
   'PYMODULE-1'),
  ('tty',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\tty.py',
   'PYMODULE-1'),
  ('typing',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\typing.py',
   'PYMODULE-1'),
  ('typing_extensions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE-1'),
  ('unittest',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\__init__.py',
   'PYMODULE-1'),
  ('unittest._log',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\_log.py',
   'PYMODULE-1'),
  ('unittest.async_case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\async_case.py',
   'PYMODULE-1'),
  ('unittest.case',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\case.py',
   'PYMODULE-1'),
  ('unittest.loader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\loader.py',
   'PYMODULE-1'),
  ('unittest.main',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\main.py',
   'PYMODULE-1'),
  ('unittest.mock',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\mock.py',
   'PYMODULE-1'),
  ('unittest.result',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\result.py',
   'PYMODULE-1'),
  ('unittest.runner',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\runner.py',
   'PYMODULE-1'),
  ('unittest.signals',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\signals.py',
   'PYMODULE-1'),
  ('unittest.suite',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\suite.py',
   'PYMODULE-1'),
  ('unittest.util',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\unittest\\util.py',
   'PYMODULE-1'),
  ('urllib',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\urllib\\__init__.py',
   'PYMODULE-1'),
  ('urllib.error',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\urllib\\error.py',
   'PYMODULE-1'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\urllib\\parse.py',
   'PYMODULE-1'),
  ('urllib.request',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\urllib\\request.py',
   'PYMODULE-1'),
  ('urllib.response',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\urllib\\response.py',
   'PYMODULE-1'),
  ('urllib3',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\__init__.py',
   'PYMODULE-1'),
  ('urllib3._base_connection',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE-1'),
  ('urllib3._collections',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\_collections.py',
   'PYMODULE-1'),
  ('urllib3._request_methods',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE-1'),
  ('urllib3._version',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\_version.py',
   'PYMODULE-1'),
  ('urllib3.connection',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\connection.py',
   'PYMODULE-1'),
  ('urllib3.connectionpool',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE-1'),
  ('urllib3.contrib',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE-1'),
  ('urllib3.contrib.emscripten',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE-1'),
  ('urllib3.contrib.emscripten.connection',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE-1'),
  ('urllib3.contrib.emscripten.fetch',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE-1'),
  ('urllib3.contrib.emscripten.request',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE-1'),
  ('urllib3.contrib.emscripten.response',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE-1'),
  ('urllib3.contrib.pyopenssl',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE-1'),
  ('urllib3.contrib.socks',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE-1'),
  ('urllib3.exceptions',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE-1'),
  ('urllib3.fields',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\fields.py',
   'PYMODULE-1'),
  ('urllib3.filepost',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\filepost.py',
   'PYMODULE-1'),
  ('urllib3.http2',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE-1'),
  ('urllib3.http2.connection',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE-1'),
  ('urllib3.http2.probe',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE-1'),
  ('urllib3.poolmanager',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE-1'),
  ('urllib3.response',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\response.py',
   'PYMODULE-1'),
  ('urllib3.util',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE-1'),
  ('urllib3.util.connection',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE-1'),
  ('urllib3.util.proxy',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE-1'),
  ('urllib3.util.request',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE-1'),
  ('urllib3.util.response',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE-1'),
  ('urllib3.util.retry',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE-1'),
  ('urllib3.util.ssl_',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE-1'),
  ('urllib3.util.ssl_match_hostname',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE-1'),
  ('urllib3.util.ssltransport',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE-1'),
  ('urllib3.util.timeout',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE-1'),
  ('urllib3.util.url',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE-1'),
  ('urllib3.util.util',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE-1'),
  ('urllib3.util.wait',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE-1'),
  ('uuid',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\uuid.py',
   'PYMODULE-1'),
  ('webbrowser',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\webbrowser.py',
   'PYMODULE-1'),
  ('xml',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\__init__.py',
   'PYMODULE-1'),
  ('xml.dom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE-1'),
  ('xml.dom.NodeFilter',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE-1'),
  ('xml.dom.domreg',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE-1'),
  ('xml.dom.expatbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE-1'),
  ('xml.dom.minicompat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE-1'),
  ('xml.dom.minidom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE-1'),
  ('xml.dom.pulldom',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE-1'),
  ('xml.dom.xmlbuilder',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE-1'),
  ('xml.etree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE-1'),
  ('xml.etree.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE-1'),
  ('xml.etree.ElementPath',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE-1'),
  ('xml.etree.ElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE-1'),
  ('xml.etree.cElementTree',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE-1'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE-1'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE-1'),
  ('xml.sax',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE-1'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE-1'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE-1'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\sax\\handler.py',
   'PYMODULE-1'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE-1'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE-1'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE-1'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\xmlrpc\\client.py',
   'PYMODULE-1'),
  ('yaml',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\yaml\\__init__.py',
   'PYMODULE-1'),
  ('yaml.composer',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\yaml\\composer.py',
   'PYMODULE-1'),
  ('yaml.constructor',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\yaml\\constructor.py',
   'PYMODULE-1'),
  ('yaml.cyaml',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\yaml\\cyaml.py',
   'PYMODULE-1'),
  ('yaml.dumper',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\yaml\\dumper.py',
   'PYMODULE-1'),
  ('yaml.emitter',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\yaml\\emitter.py',
   'PYMODULE-1'),
  ('yaml.error',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\yaml\\error.py',
   'PYMODULE-1'),
  ('yaml.events',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\yaml\\events.py',
   'PYMODULE-1'),
  ('yaml.loader',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\yaml\\loader.py',
   'PYMODULE-1'),
  ('yaml.nodes',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\yaml\\nodes.py',
   'PYMODULE-1'),
  ('yaml.parser',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\yaml\\parser.py',
   'PYMODULE-1'),
  ('yaml.reader',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\yaml\\reader.py',
   'PYMODULE-1'),
  ('yaml.representer',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\yaml\\representer.py',
   'PYMODULE-1'),
  ('yaml.resolver',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\yaml\\resolver.py',
   'PYMODULE-1'),
  ('yaml.scanner',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\yaml\\scanner.py',
   'PYMODULE-1'),
  ('yaml.serializer',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\yaml\\serializer.py',
   'PYMODULE-1'),
  ('yaml.tokens',
   'D:\\software\\code\\FlashToolApp\\.venv\\Lib\\site-packages\\yaml\\tokens.py',
   'PYMODULE-1'),
  ('zipfile',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipfile\\__init__.py',
   'PYMODULE-1'),
  ('zipfile._path',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipfile\\_path\\__init__.py',
   'PYMODULE-1'),
  ('zipfile._path.glob',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipfile\\_path\\glob.py',
   'PYMODULE-1'),
  ('zipimport',
   'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\zipimport.py',
   'PYMODULE-1')])
