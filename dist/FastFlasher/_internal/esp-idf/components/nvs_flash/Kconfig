menu "NVS"

    config NVS_ENCRYPTION
        bool "Enable NVS encryption"
        depends on SECURE_FLASH_ENC_ENABLED || SOC_HMAC_SUPPORTED
        default y if SECURE_FLASH_ENC_ENABLED
        help
            This option enables encryption for NVS. When enabled, XTS-AES is used to encrypt
            the complete NVS data, except the page headers. It requires XTS encryption keys
            to be stored in an encrypted partition (enabling flash encryption is mandatory here)
            or to be derived from an HMAC key burnt in eFuse.

    config NVS_COMPATIBLE_PRE_V4_3_ENCRYPTION_FLAG
        bool "NVS partition encrypted flag compatible with ESP-IDF before v4.3"
        depends on SECURE_FLASH_ENC_ENABLED
        help
            Enabling this will ignore "encrypted" flag for NVS partitions. NVS encryption
            scheme is different than hardware flash encryption and hence it is not recommended
            to have "encrypted" flag for NVS partitions. This was not being checked in pre v4.3
            IDF. Hence, if you have any devices where this flag is kept enabled in partition
            table then enabling this config will allow to have same behavior as pre v4.3 IDF.

    config NVS_ASSERT_ERROR_CHECK
        bool "Use assertions for error checking"
        default n
        help
            This option switches error checking type between assertions (y) or return codes (n).

    config NVS_LEGACY_DUP_KEYS_COMPATIBILITY
        bool "Enable legacy nvs_set function behavior when same key is reused with different data types"
        default n
        help
            Enabling this option will switch the nvs_set() family of functions to the legacy mode:
            when called repeatedly with the same key but different data type, the existing value
            in the NVS remains active and the new value is just stored, actually not accessible through
            corresponding nvs_get() call for the key given. Use this option only when your application
            relies on such NVS API behaviour.
endmenu
