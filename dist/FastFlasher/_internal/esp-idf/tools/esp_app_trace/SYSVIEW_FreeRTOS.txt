128        vTaskAllocateMPURegions             xTask=%t pxRegions=%u
33        vTaskDelete                         xTaskToDelete=%t
34        vTaskDelay                          xTicksToDelay=%u
35        vTaskDelayUntil
129        uxTaskPriorityGet                   xTask=%t
56        uxTaskPriorityGetFromISR            xTask=%t
130        eTaskGetState                       xTask=%t
55        vTaskPrioritySet                    xTask=%t uxNewPriority=%u
36        vTaskSuspend                        xTaskToSuspend=%t
40        vTaskResume                         xTaskToResume=%t
43        xTaskResumeFromISR                  xTaskToResume=%t
131        vTaskStartScheduler
132        vTaskEndScheduler
133        vTaskSuspendAll
134        xTaskResumeAll
135        xTaskGetTickCount
57        xTaskGetTickCountFromISR
136        uxTaskGetNumberOfTasks
137        pcTaskGetName                       xTaskToQuery=%t
138        uxTaskGetStackHighWaterMark         xTask=%t
139        vTaskSetApplicationTaskTag          xTask=%t pxHookFunction=%u
140        xTaskGetApplicationTaskTag          xTask=%t
141        vTaskSetThreadLocalStoragePointer   xTaskToSet=%T xIndex=%u pvValue=%u
142        pvTaskGetThreadLocalStoragePointer  xTaskToQuery=%T xIndex=%u
143        xTaskCallApplicationTaskHook        xTask=%T pvParameter=%u
144        xTaskGetIdleTaskHandle
145        uxTaskGetSystemState                pxTaskStatusArray=%u uxArraySize=%u pulTotalRunTime=%u
146        vTaskList                           pcWriteBuffer=%u
147        vTaskGetRunTimeStats                pcWriteBuffer=%u
44        xTaskGenericNotify                  xTaskToNotify=%t ulValue=%u eAction=%u pulPreviousNotificationValue=%u
45        xTaskGenericNotifyFromISR           xTaskToNotify=%t ulValue=%u eAction=%u pulPreviousNotificationValue=%u pxHigherPriorityTaskWoken=%u
46        xTaskNotifyWait                     ulBitsToClearOnEntry=%u ulBitsToClearOnExit=%u pulNotificationValue=%u xTicksToWait=%u
38        vTaskNotifyGiveFromISR              xTaskToNotify=%t pxHigherPriorityTaskWoken=%u
37        ulTaskNotifyTake                    xClearCountOnExit=%u xTicksToWait=%u
148        xTaskNotifyStateClear               xTask=%t
149        xTaskGetCurrentTaskHandle
150        vTaskSetTimeOutState                pxTimeOut=%u
151        xTaskCheckForTimeOut                pxTimeOut=%u pxTicksToWait=%u
152        vTaskMissedYield
153        xTaskGetSchedulerState
39        vTaskPriorityInherit                pxMutexHolder=%p
42        xTaskPriorityDisinherit             pxMutexHolder=%p
154        xTaskGenericCreate                  pxTaskCode=%u pcName=%u usStackDepth=%u pvParameters=%u uxPriority=%u pxCreatedTask=%u puxStackBuffer=%u xRegions=%u
155        uxTaskGetTaskNumber                 xTask=%u
156        vTaskSetTaskNumber                  xTask=%u uxHandle=%u
41        vTaskStepTick                       xTicksToJump=%u
157        eTaskConfirmSleepModeStatus
158        xTimerCreate                        pcTimerName=%u xTimerPeriodInTicks=%u uxAutoReload=%u pvTimerID=%u pxCallbackFunction=%u
159        pvTimerGetTimerID                   xTimer=%u
160        vTimerSetTimerID                    xTimer=%u pvNewID=%u
161        xTimerIsTimerActive                 xTimer=%u
162        xTimerGetTimerDaemonTaskHandle
163        xTimerPendFunctionCallFromISR       xFunctionToPend=%u pvParameter1=%u ulParameter2=%u pxHigherPriorityTaskWoken=%u
164        xTimerPendFunctionCall              xFunctionToPend=%u pvParameter1=%u ulParameter2=%u xTicksToWait=%u
165        pcTimerGetName                      xTimer=%u
166        xTimerCreateTimerTask
167        xTimerGenericCommand                xTimer=%u xCommandID=%u xOptionalValue=%u pxHigherPriorityTaskWoken=%u xTicksToWait=%u
53        xQueueGenericSend                   xQueue=%I pvItemToQueue=%p xTicksToWait=%u xCopyPosition=%u
50        xQueuePeekFromISR                   xQueue=%I pvBuffer=%p
49        xQueueGenericReceive                xQueue=%I pvBuffer=%p xTicksToWait=%u xJustPeek=%u
168        uxQueueMessagesWaiting              xQueue=%I
169        uxQueueSpacesAvailable              xQueue=%I
48        vQueueDelete                        xQueue=%I
54        xQueueGenericSendFromISR            xQueue=%I pvItemToQueue=%p pxHigherPriorityTaskWoken=%u xCopyPosition=%u
61        xQueueGiveFromISR                   xQueue=%I pxHigherPriorityTaskWoken=%u
51        xQueueReceiveFromISR                xQueue=%I pvBuffer=%p pxHigherPriorityTaskWoken=%u
62        xQueueIsQueueEmptyFromISR           xQueue=%I
63       xQueueIsQueueFullFromISR            xQueue=%I
170       uxQueueMessagesWaitingFromISR       xQueue=%I
171       xQueueAltGenericSend                xQueue=%I pvItemToQueue=%p xTicksToWait=%u xCopyPosition=%u
172       xQueueAltGenericReceive             xQueue=%I pvBuffer=%p xTicksToWait=%u xJustPeeking=%u
173       xQueueCRSendFromISR                 xQueue=%I pvItemToQueue=%p xCoRoutinePreviouslyWoken=%u
174       xQueueCRReceiveFromISR              xQueue=%I pvBuffer=%p pxTaskWoken=%u
175       xQueueCRSend                        xQueue=%I pvItemToQueue=%p xTicksToWait=%u
176       xQueueCRReceive                     xQueue=%I pvBuffer=%p xTicksToWait=%u
177       xQueueCreateMutex                   ucQueueType=%u
178       xQueueCreateCountingSemaphore       uxMaxCount=%u uxInitialCount=%u
179       xQueueGetMutexHolder                xSemaphore=%u
180       xQueueTakeMutexRecursive            xMutex=%u xTicksToWait=%u
181       xQueueGiveMutexRecursive            pxMutex=%u
52       vQueueAddToRegistry                 xQueue=%I pcName=%u
182       vQueueUnregisterQueue               xQueue=%I
47       xQueueGenericCreate                 uxQueueLength=%u uxItemSize=%u ucQueueType=%u
183       xQueueCreateSet                     uxEventQueueLength=%u
184       xQueueAddToSet                      xQueueOrSemaphore=%u xQueueSet=%u
185       xQueueRemoveFromSet                 xQueueOrSemaphore=%u xQueueSet=%u
186       xQueueSelectFromSet                 xQueueSet=%u xTicksToWait=%u
187       xQueueSelectFromSetFromISR          xQueueSet=%u
188       xQueueGenericReset                  xQueue=%I xNewQueue=%u
189       vListInitialise                     pxList=%u
190       vListInitialiseItem                 pxItem=%u
191       vListInsert                         pxList=%u pxNewListItem=%u
192       vListInsertEnd                      pxList=%u pxNewListItem=%u
193       uxListRemove                        pxItemToRemove=%u
194       xEventGroupCreate
195       xEventGroupWaitBits                 xEventGroup=%u uxBitsToWaitFor=%u xClearOnExit=%u xWaitForAllBits=%u xTicksToWait=%u
196       xEventGroupClearBits                xEventGroup=%u uxBitsToClear=%u
58       xEventGroupClearBitsFromISR         xEventGroup=%u uxBitsToSet=%u
197       xEventGroupSetBits                  xEventGroup=%u uxBitsToSet=%u
59       xEventGroupSetBitsFromISR           xEventGroup=%u uxBitsToSet=%u pxHigherPriorityTaskWoken=%u
198       xEventGroupSync                     xEventGroup=%u uxBitsToSet=%u uxBitsToWaitFor=%u xTicksToWait=%u
60       xEventGroupGetBitsFromISR           xEventGroup=%u
199       vEventGroupDelete                   xEventGroup=%u
200       uxEventGroupGetNumber               xEventGroup=%u
201       xStreamBufferCreate                 xIsMessageBuffer=%u pxStreamBuffer=%p
202       vStreamBufferDelete                 xStreamBuffer=%p
203       xStreamBufferReset                  xStreamBuffer=%p
204       xStreamBufferSend                   xStreamBuffer=%p xBytesSent=%u
205       xStreamBufferSendFromISR            xStreamBuffer=%p xBytesSent=%u
206       xStreamBufferReceive                xStreamBuffer=%p xReceivedLength=%u
207       xStreamBufferReceiveFromISR         xStreamBuffer=%p xReceivedLength=%u

512       esp_sysview_heap_trace_alloc        addr=%p size=%u callers=%x
513       esp_sysview_heap_trace_free        addr=%p callers=%x
