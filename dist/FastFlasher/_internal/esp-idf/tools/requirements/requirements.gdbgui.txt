# Python package requirements for gdbgui support ESP-IDF.
# This feature can be enabled by running "install.{sh,bat,ps1,fish} --enable-gdbgui"

# gdbgui Python 3.11 issue https://github.com/cs01/gdbgui/issues/447 was fixed in 0.15.2.0. Windows users need an
# older Python to use since new gdbgui versions don't support Windows anymore.
gdbgui; sys_platform != 'win32'
gdbgui; sys_platform == 'win32' and python_version < "3.11"
