# Special partition table for unit test app
#
# Name,     Type, SubType, Offset,   Size, Flags
# Note: if you have increased the bootloader size, make sure to update the offsets to avoid overlap
nvs,        data, nvs,      0xb000,  0x5000
otadata,    data, ota,      0x10000,  0x2000
phy_init,   data, phy,      0x12000,  0x1000
factory,    0,    0,        0x20000, 0x150000
# these OTA partitions are used for tests, but can't fit real OTA apps in them
# (done this way to reduce total flash usage.)
ota_0,      0,    ota_0,    ,        64K
ota_1,      0,    ota_1,    ,        64K
# flash_test partition used for SPI flash tests, WL FAT tests, and SPIFFS tests
flash_test, data, fat,      ,        320K
nvs_key,    data, nvs_keys, ,        0x1000, encrypted

# Note: occupied 1.85MB in the 2MB flash
