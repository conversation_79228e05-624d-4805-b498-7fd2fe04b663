set(srcs "memory_checks.c"
         "test_runner.c"
         "test_utils.c")

if(CONFIG_IDF_TARGET_ESP32)
    # ESP32's timer group doesn't have XTAL clock source,
    # so we can't implement a timekeeping that can work during DFS
    # but we can work around that by combining RMT and PCNT
    # where PCNT can count the pulses generated by RMT, and RMT is clocked from REF_TICK
    # REF_TICK won't be affected by DFS
    list(APPEND srcs "ref_clock_impl_rmt_pcnt.c")
else()
    list(APPEND srcs "ref_clock_impl_timergroup.c")
endif()

idf_component_register(SRCS ${srcs}
                    INCLUDE_DIRS include
                    REQUIRES esp_partition idf_test cmock
                    PRIV_REQUIRES perfmon esp_driver_pcnt esp_driver_gptimer esp_driver_rmt esp_netif)
target_compile_options(${COMPONENT_LIB} PRIVATE "-Wno-format")
