[pytest]

# log related
log_cli = True
log_cli_level = INFO
log_cli_format = %(asctime)s %(levelname)s %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# junit related
junit_family = xunit1

## log all to `system-out` when case fail
junit_logging = stdout
junit_log_passing_tests = False

markers =
    test_app_copy: specify relative path of the app to copy, and the prefix of the destination directory name
    idf_copy: specify the prefix of the destination directory where IDF should be copied
    force_temp_work_dir: force temporary folder as the working directory
